<?php
require __DIR__ . "/../core/let.php";
if ($islogin != 1) @header("Location: ./login.php");
if (!is_file(__DIR__ . "/../core/list.json")) @file_put_contents(__DIR__ . "/../core/list.json", "[]");
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>后台管理中心</title>
    <link rel="stylesheet" href="//cdn.staticfile.org/layui/2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../assets/css/admin.css" media="all">
    <link rel="stylesheet" href="../assets/css/login.css" media="all">
</head>
<body>

<div class="layui-container">
    <fieldset class="layui-elem-field layui-field-title" align="center" style="margin-top: 20px;">
        <legend >后台管理中心 </legend>
    </fieldset>

    <blockquote class="layui-elem-quote" style="margin-top: 30px;">
        <div class="layui-text">
            <li><span>客服QQ：奇偶猫</span></li>
        </div>
    </blockquote>

    <div class="layui-tab layui-tab-brief" lay-filter="tabDemo">
        <ul class="layui-tab-title">
            <li class="layui-this">我的信息</li>
            <li>链接列表</li>
            <li>新增链接</li>
            <li><a href="./login.php?do=logout">退出登录</a></li>
        </ul>
    <div class="layui-tab-content">

        <div class="layui-tab-item layui-show">
            <div class="layadmin-user-login-box layadmin-user-login-body layui-form">
            <div class="layui-form-item">
                    <label class="layui-form-label">登录账号</label>
                    <div class="layui-input-block">
                        <input type="text" name="username" lay-verify="required" autocomplete="off" value="<?php echo $admin_config["username"];?>" placeholder="请输入登录账号" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">登录密码</label>
                    <div class="layui-input-block">
                        <input type="password" name="password" placeholder="不修改则无需填写" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="edit">修改</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-tab-item">
            <table class="layui-hide" id="test" lay-filter="test"></table>
        </div>

        <div class="layui-tab-item">
            <div class="layadmin-user-login-box layadmin-user-login-body layui-form">
                <div class="layui-form-item">
                    <label class="layui-form-label">订单号</label>
                    <div class="layui-input-block">
                        <input type="text" name="orderid" lay-verify="required" autocomplete="off" placeholder="请输入支付宝订单号" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">备注</label>
                    <div class="layui-input-block">
                        <input type="text" name="beizhu" placeholder="用于区分，非必填" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="add">新增</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

 
<script src="//cdn.staticfile.org/layui/2.6.8/layui.js"></script>

<script>
layui.use('element', function(){
    var element = layui.element;
    //一些事件
    element.on('tab(tabDemo)', function(data){
        console.log(this, data);
    });
});
</script>

<script>
layui.use(['form', 'layer'], function(){
    var form = layui.form,
      layer = layui.layer;
    form.on('submit(add)', function(data) {
        layui.use(['jquery', 'layer'], function() {
            var $ = layui.jquery;
            var layer = layui.layer;
            $.ajax({
                url: './ajax.php?act=add',
                type: 'POST',
                dataType: 'json',
                data: data.field,
                success : function(res) {
                    if (res.code == "ok") {
				        layer.alert(res.msg + "<br>链接：" + res.url + "<br><img src='https://api.btstu.cn/qrcode/api.php?text=" + res.url + "&size=300'>", {
                            icon: 1
                        });
			        } else {
				        layer.msg(res.msg, {
                            icon: 2,
                            time: 1000
                        });
		            }
                },
                error : function(XMLHttpRequest, textStatus, errorThrown) {
                    layer.msg('服务器错误', {
                        icon: 2
                    });
			        return false;
                }
            });
        });
    });

    form.on('submit(edit)', function(data){
        layui.use(['jquery', 'layer'], function() {
            var $ = layui.jquery;
            var layer = layui.layer;
            $.ajax({
                url: './ajax.php?act=change',
                type: 'POST',
                dataType: 'json',
                data: data.field,
                success : function(res) {
                    if (res.code == "ok") {
				        layer.alert(res.msg, {
                            icon: 1
                        }, function() {
					        window.location.href = "./login.php";
				        });
			        } else {
				        layer.msg(res.msg, {
                            icon: 2,
                            time: 1000
                        });
		            }
                },
                error : function(XMLHttpRequest, textStatus, errorThrown) {
                    layer.msg('服务器错误', {
                        icon: 2
                    });
			        return false;
                }
            });
        });
    });

});
</script>

<script type="text/html" id="barDemo">
    <a class="layui-btn layui-btn-xs" lay-event="query">查看</a>
    <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="delete">删除</a>
</script>

<script>
layui.use(['table', 'dropdown'], function(){
    var table = layui.table;
    var dropdown = layui.dropdown;

    // 创建渲染实例
    var insTb = table.render({
        elem: '#test',
        url:'./ajax.php?act=list',
        text: {
            none: '报告主人，没有发现数据！ʕ  •ᴥ•ʔ……'
        },
        height: 'full-250', // 最大高度减去其他容器已占有的高度差
        cellMinWidth: 0,
        page: false,
        cols: [[
            {field:'id',  width: 155, title: 'ID(标识)', sort: true, unresize: true},
            {field:'orderid', width: 180, title: '订单号', unresize: true},
            {field:'beizhu', width: 145, title: '备注', unresize: true},
            {field:'addtime',  width: 180, title: '创建时间', sort: true, unresize: true},
            {field:'center', title: '操作', width: 220, style: '-moz-box-align: start;' ,fixed:'right',toolbar:'#barDemo',align:"center",unresize:true}
        ]]
    });

    table.on('tool(test)', function(obj) {
        var layEvent = obj.event;
        var tr = obj.tr;
        var $ = layui.jquery;
        if (layEvent === 'query') { //查看
            $.ajax({
                url: './ajax.php?act=query',
                type: 'POST',
                dataType: 'json',
                data: obj.data,
                success : function (res) {
                    if (res.code == "ok") {
				        layer.alert(res.msg + "<br>链接：" + res.url + "<br><img src='https://api.btstu.cn/qrcode/api.php?text=" + res.url + "&size=300'>", {
                            icon: 1
                        });
			        } else {
				        layer.msg(res.msg, {
                            icon: 2,
                            time: 1000
                        });
		            }
                },
                error : function () {
                    layer.msg('服务器错误', {
                        icon: 2
                    });
			        return false;
                }
            });
        } else if (layEvent === 'delete') { //删除
            $.ajax({
                url: './ajax.php?act=delete',
                type: 'POST',
                dataType: 'json',
                data: obj.data,
                success : function (res) {
                    if (res.code == "ok") {
				        layer.msg(res.msg, {
                            icon: 1,
                            time: 1000
                        });
                        insTb.reload({page: {curr: 1}});
			        } else {
				        layer.msg(data.msg, {
                            icon: 2,
                            time: 1000
                        });
		            }
                },
                error : function () {
                    layer.msg('服务器错误', {
                        icon: 2
                    });
			        return false;
                }
            });
        }
    });

});
</script>

</div>


</body>
<footer class="layui-trans layadmin-user-login-footer">
      
  <p><?php echo $conf["name"];?> © All Rights Reserved</p>

</footer>
</html>