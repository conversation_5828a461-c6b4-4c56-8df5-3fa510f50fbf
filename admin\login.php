<?php
require __DIR__ . "/../core/let.php";

if (@$_GET["do"] == "logout") {
    $_SESSION["admin"] = null;
    @header("Content-Type: text/html; charset=UTF-8");
    exit("<script language='javascript'>alert('您已成功注销本次登录！');window.location.href='./login.php';</script>");
} else if ($islogin == 1) {
    @header("Location: ./");
} else if (!empty($_POST["user"]) && !empty($_POST["pwd"])) {
    $username = $_POST["user"];
    $password = $_POST["pwd"];
    !empty($_POST["code"]) ? $captcha = $_POST["code"] : exit(json(array("msg" => "请输入图形验证码！")));
    $code = @$_SESSION["captcha"];
    $_SESSION["captcha"] = null;
    if (strtolower($captcha) != $code) exit(json(array("msg" => "图形验证码不正确！")));
    if ($username == $admin_config["username"] && $password == $admin_config["password"]) {
        $_SESSION["admin"] = array(
            "username" => $username,
            "password" => $password
        );
        exit(json(array("code" => "ok", "msg" => "登录成功！")));
    } else {
        exit(json(array("msg" => "账号或密码不正确！")));
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>后台登录 - <?php echo $conf["name"];?></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="//cdn.staticfile.org/layui/2.6.8/css/layui.css" media="all">
    <link rel="stylesheet" href="../assets/css/admin.css" media="all">
    <link rel="stylesheet" href="../assets/css/login.css" media="all">
</head>
<body>
    <div class="layadmin-user-login layadmin-user-display-show" id="LAY-user-login" style="display: none;">

        <div class="layadmin-user-login-main">
            <div class="layadmin-user-login-box layadmin-user-login-header">
                <h2>后台登录</h2>
                <p> </p>
            </div>
            <div class="layadmin-user-login-box layadmin-user-login-body layui-form">
                <div class="layui-form-item">
                    <label class="layadmin-user-login-icon layui-icon layui-icon-username" for="LAY-user-login-username"></label>
                    <input type="text" name="user" id="LAY-user-login-username" lay-verify="required" placeholder="用户名" class="layui-input">
                </div>
                <div class="layui-form-item">
                    <label class="layadmin-user-login-icon layui-icon layui-icon-password" for="LAY-user-login-password"></label>
                    <input type="password" name="pwd" id="LAY-user-login-password" lay-verify="required" placeholder="密码" class="layui-input">
                </div>
                <div class="layui-form-item">
                    <div class="layui-row">
                        <div class="layui-col-xs7">
                            <label class="layadmin-user-login-icon layui-icon layui-icon-vercode" for="LAY-user-login-vercode"></label>
                            <input type="text" name="code" id="LAY-user-login-vercode" lay-verify="required" placeholder="图形验证码" class="layui-input">
                        </div>
                        <div class="layui-col-xs5">
                            <div style="margin-left: 10px;">
                                <img src="./captcha.php" onclick="this.src='./captcha.php?r='+Math.random();" title="点击更换验证码" class="layadmin-user-login-codeimg" id="LAY-user-get-vercode">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <button class="layui-btn layui-btn-fluid" lay-submit lay-filter="LAY-user-login-submit">登录</button>
                </div>
            </div>
        </div>
        <div class="layui-trans layadmin-user-login-footer">
            <p>© 2018 - <?php echo date("Y");?> <a href="#"><?php echo $conf["name"];?></a></p>
        </div>
      </div>

    <script src="//cdn.staticfile.org/layui/2.6.8/layui.js"></script>
    <script>
        layui.use(['form', 'layer'], function(){
            var $ = layui.$,
		        form = layui.form,
		        layer = layui.layer;
	        form.on('submit(LAY-user-login-submit)', function(obj) {
                var field = obj.field;
                var ii = layer.load(2, {shade:[0.1, '#fff']});
	            $.ajax({
		            type : "POST",
		            url : "",
		            data : field,
		            dataType : 'json',
		            success : function(data) {
			            layer.close(ii);
			            if (data.code == "ok") {
				            layer.alert('登录成功！', {
                                icon: 1
                            }, function() {
					            window.location.href = "./";
				            });
			            } else {
                            $("#LAY-user-login-vercode").val("");
                            $("#LAY-user-get-vercode").click(function () {
                                this.src = './captcha.php?r=' + (new Date).getTime();
                            }).trigger("click");
				            layer.msg(data.msg, {
                                icon: 2,
                                time: 1000
                            });
			            }
		            },
		            error : function(data) {
			            layer.msg('服务器错误', {
                            icon: 2
                        });
			            return false;
		            }
	            });
                return false;
            });
        });
    </script>
</body>
</html>