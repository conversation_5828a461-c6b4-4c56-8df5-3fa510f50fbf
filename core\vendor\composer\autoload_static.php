<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit2ef7b76b5b309a41d4c1b5b3a28a90bd
{
    public static $files = array (
        '9b552a3cc426e3287cc811caefa3cf53' => __DIR__ . '/..' . '/topthink/think-helper/src/helper.php',
        '538ca81a9a966a6716601ecf48f4eaef' => __DIR__ . '/..' . '/opis/closure/functions.php',
    );

    public static $prefixLengthsPsr4 = array (
        't' => 
        array (
            'think\\' => 6,
        ),
        'P' => 
        array (
            'Psr\\SimpleCache\\' => 16,
            'Psr\\Container\\' => 14,
            'Psr\\Cache\\' => 10,
        ),
        'O' => 
        array (
            'Opis\\Closure\\' => 13,
        ),
        'F' => 
        array (
            'FC\\' => 3,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'think\\' => 
        array (
            0 => __DIR__ . '/..' . '/topthink/think-cache/src',
            1 => __DIR__ . '/..' . '/topthink/think-container/src',
            2 => __DIR__ . '/..' . '/topthink/think-helper/src',
        ),
        'Psr\\SimpleCache\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/simple-cache/src',
        ),
        'Psr\\Container\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/container/src',
        ),
        'Psr\\Cache\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/cache/src',
        ),
        'Opis\\Closure\\' => 
        array (
            0 => __DIR__ . '/..' . '/opis/closure/src',
        ),
        'FC\\' => 
        array (
            0 => __DIR__ . '/..' . '/lovefc/captcha/Src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit2ef7b76b5b309a41d4c1b5b3a28a90bd::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit2ef7b76b5b309a41d4c1b5b3a28a90bd::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit2ef7b76b5b309a41d4c1b5b3a28a90bd::$classMap;

        }, null, ClassLoader::class);
    }
}
