<?php
use think\facade\Cache;
require __DIR__ . "/../core/let.php";

if ($islogin != 1) exit(json(array("msg" => "请先登录")));

!empty(@$_GET["act"]) ? $act = @$_GET["act"] : exit(json(array("msg" => "no act!")));

switch ($act) {
    case "list" :
        $ret = json_decode(@file_get_contents(__DIR__ . "/../core/list.json"), true);
        exit(json(array(
            "code" => 0,
            "msg" => "获取成功",
            "count" => count($ret),
            "data" => $ret
        ), 2));
    break;
    case "change" :
        !empty(@$_POST["username"]) ? $username = @$_POST["username"] : exit(json(array("msg" => "登录账号不可留空！")));
        if (!preg_match("/^[a-zA-Z0-9\x7f-\xff]+$/", $username)) exit(json(array("msg" => "用户名只能为英文、数字与汉字！")));
        if (!empty(@$_POST["password"])) {
            $password = @$_POST["password"];
            if (strlen($password) < 6) exit(json(array("msg" => "密码不能低于6位！")));
        } else {
            $password = $admin_config["password"];
        }
        $ret = @file_put_contents(__DIR__ . "/../core/admin.config.php", '<?php

        return array(
            "username" => "' . $username . '",
            "password" => "' . $password . '"
        );
        
        ?>');
        if (!$ret) exit(json(array("msg" => "修改失败，请检查是否有写入权限！")));
        exit(json(array("code" => "ok", "msg" => "修改成功，请重新登录！")));
    break;
    case "add" :
        !empty(@$_POST["orderid"]) ? $orderid = @$_POST["orderid"] : exit(json(array("msg" => "订单号不可留空！")));
        $beizhu = @$_POST["beizhu"];
        $date = date("Y-m-d H:i:s");
        $id = date("YmdHis") . mt_rand(10000, 99999);
        $data = json_decode(@file_get_contents(__DIR__ . "/../core/list.json"), true);
        $data[] = array(
            "id" => $id,
            "orderid" => $orderid,
            "beizhu" => $beizhu,
            "addtime" => $date
        );
        Cache::set($id, array(
            "orderid" => $orderid,
            "addtime" => $date
        ));
        $ret = @file_put_contents(__DIR__ . "/../core/list.json", json_encode($data));
        if (!$ret) exit(json(array("msg" => "新增失败，请检查是否有写入权限！")));
        $host_url = (is_https() ? "https://" : "http://") . getenv("HTTP_HOST") . "/h5.php/taobao/$id";
        exit(json(array("code" => "ok", "msg" => "新增成功！", "url" => $host_url)));
    break;
    case "delete" :
        !empty(@$_POST["id"]) ? $id = @$_POST["id"] : exit(json(array("msg" => "id is empty!")));
        $data = json_decode(@file_get_contents(__DIR__ . "/../core/list.json"), true);
        $is = false;
        for ($i = 0; $i < count($data); $i++) {
            if ($data[$i]["id"] == $id) {
                $ret_i = $i;
                $is = true;
            }
        }
        if (!$is) exit(json(array("msg" => "未找到指定数据！")));
        array_splice($data, $ret_i, 1);
        $ret = @file_put_contents(__DIR__ . "/../core/list.json", json_encode($data));
        if (!$ret) exit(json(array("msg" => "新增失败，请检查是否有写入权限！")));
        Cache::delete($id);
        exit(json(array("code" => "ok", "msg" => "删除成功！")));
    break;
    case "query" :
        !empty(@$_POST["id"]) ? $id = @$_POST["id"] : exit(json(array("msg" => "id is empty!")));
        $data = json_decode(@file_get_contents(__DIR__ . "/../core/list.json"), true);
        $host_url = (is_https() ? "https://" : "http://") . getenv("HTTP_HOST") . "/h5.php/taobao/$id";
        exit(json(array("code" => "ok", "msg" => "查询成功！", "url" => $host_url)));
    break;
}

?>