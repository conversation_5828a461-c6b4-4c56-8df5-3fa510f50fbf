{"packages": [{"name": "lovefc/captcha", "version": "0.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/lovefc/captcha.git", "reference": "bf0827cca00c1d432766132cf88cfb0977bf19b7"}, "dist": {"type": "zip", "url": "https://repo.huaweicloud.com/repository/php/lovefc/captcha/0.0.4/lovefc-captcha-0.0.4.zip", "reference": "bf0827cca00c1d432766132cf88cfb0977bf19b7", "shasum": ""}, "require": {"php": ">=5.6.0"}, "time": "2022-07-05T08:58:11+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"FC\\": "Src"}}, "license": ["MIT"], "authors": [{"name": "lovefc", "email": "<EMAIL>", "role": "Developer"}], "description": "php中文验证码", "homepage": "https://github.com/lovefc/captcha.git", "keywords": ["<PERSON><PERSON>a", "php", "中文验证码", "验证码"], "install-path": "../lovefc/captcha"}, {"name": "opis/closure", "version": "3.6.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/opis/closure.git", "reference": "3d81e4309d2a927abbe66df935f4bb60082805ad"}, "dist": {"type": "zip", "url": "https://repo.huaweicloud.com/repository/php/opis/closure/3.6.3/opis-closure-3.6.3.zip", "reference": "3d81e4309d2a927abbe66df935f4bb60082805ad", "shasum": ""}, "require": {"php": "^5.4 || ^7.0 || ^8.0"}, "require-dev": {"jeremeamia/superclosure": "^2.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "time": "2022-01-27T09:35:39+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.6.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["functions.php"], "psr-4": {"Opis\\Closure\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Sorin Sarca", "email": "<EMAIL>"}], "description": "A library that can be used to serialize closures (anonymous functions) and arbitrary objects.", "homepage": "https://opis.io/closure", "keywords": ["anonymous functions", "closure", "function", "serializable", "serialization", "serialize"], "install-path": "../opis/closure"}, {"name": "psr/cache", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://repo.huaweicloud.com/repository/php/psr/cache/1.0.1/psr-cache-1.0.1.zip", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2016-08-06T20:24:11+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "install-path": "../psr/cache"}, {"name": "psr/container", "version": "2.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://repo.huaweicloud.com/repository/php/psr/container/2.0.2/psr-container-2.0.2.zip", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "time": "2021-11-05T16:47:00+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "install-path": "../psr/container"}, {"name": "psr/simple-cache", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://repo.huaweicloud.com/repository/php/psr/simple-cache/1.0.1/psr-simple-cache-1.0.1.zip", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2017-10-23T01:57:42+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "install-path": "../psr/simple-cache"}, {"name": "topthink/think-cache", "version": "v2.0.7", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-cache.git", "reference": "7b6ace7eb9b569fe95000b254000bbafa3c7dfee"}, "dist": {"type": "zip", "url": "https://repo.huaweicloud.com/repository/php/topthink/think-cache/v2.0.7/topthink-think-cache-v2.0.7.zip", "reference": "7b6ace7eb9b569fe95000b254000bbafa3c7dfee", "shasum": ""}, "require": {"opis/closure": "^3.1", "php": ">=7.1.0", "psr/cache": "~1.0", "psr/simple-cache": "^1.0", "topthink/think-container": "~2.0"}, "time": "2023-01-16T08:09:09+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": [], "psr-4": {"think\\": "src"}}, "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "<PERSON><PERSON> Manager", "install-path": "../topthink/think-cache"}, {"name": "topthink/think-container", "version": "v2.0.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-container.git", "reference": "2189b39e42af2c14203ed4372b92e38989e9dabb"}, "dist": {"type": "zip", "url": "https://repo.huaweicloud.com/repository/php/topthink/think-container/v2.0.5/topthink-think-container-v2.0.5.zip", "reference": "2189b39e42af2c14203ed4372b92e38989e9dabb", "shasum": ""}, "require": {"php": ">=7.2.0", "psr/container": "^1.0|^2.0", "topthink/think-helper": "^3.1"}, "require-dev": {"phpunit/phpunit": "^7.0|^8.0"}, "time": "2022-05-23T06:24:54+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": [], "psr-4": {"think\\": "src"}}, "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "PHP Container & Facade Manager", "install-path": "../topthink/think-container"}, {"name": "topthink/think-helper", "version": "v3.1.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-helper.git", "reference": "769acbe50a4274327162f9c68ec2e89a38eb2aff"}, "dist": {"type": "zip", "url": "https://repo.huaweicloud.com/repository/php/topthink/think-helper/v3.1.6/topthink-think-helper-v3.1.6.zip", "reference": "769acbe50a4274327162f9c68ec2e89a38eb2aff", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "time": "2021-12-15T04:27:55+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/helper.php"], "psr-4": {"think\\": "src"}}, "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP6 Helper Package", "install-path": "../topthink/think-helper"}], "dev": true, "dev-package-names": []}