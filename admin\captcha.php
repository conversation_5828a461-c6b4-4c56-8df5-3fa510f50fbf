<?php

use FC\Captcha;

require __DIR__ . "/../core/let.php";

/* 实例化 */
$ver = new Captcha();

/* 验证码的一些设置 */

// 验证码宽度
$ver->width = 300;

// 验证码高度
$ver->height = 100;

// 验证码个数
$ver->nums = 4;

// 随机字符串
$ver->random = '0123456789';

// 随机数大小
$ver->font_size = 40;

// 干扰线数量，为0时没有干扰线
$ver->interfere_line = mt_rand(5, 11);

// 是否为动态验证码
$ver->is_gif = true;

// 动图帧数
$ver->gif_fps = 5;

/* 生成验证码 */
$code = $ver->getCode();

$_SESSION["captcha"] = $code;

/* 生成验证码图片 */
$ver->doImg($code);

?>