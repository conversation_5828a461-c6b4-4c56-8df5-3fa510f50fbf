<?php

// 开启会话
session_start();

// 设置时区
date_default_timezone_set("PRC");

// 引用依赖库
require __DIR__ . "/vendor/autoload.php";

// 后台信息
$admin_config = require __DIR__ . "/admin.config.php";

// 系统信息
$conf = array(
	"name" => "苏宇收货",
	"kfqq" => "3392595834"
);

// 缓存配置
use think\facade\Cache;
Cache::config([
	'default'	=>	'file',
	'stores'	=>	[
		'file'	=>	[
			'type'   => 'File',
			// 缓存保存目录
			'path'   =>  __DIR__ . '/runtime/',
			// 缓存前缀
			'prefix' => '',
			// 缓存有效期 0表示永久缓存
			'expire' => 0,
		]
	]
]);

// 是否https
function is_https() {
    if ( !empty($_SERVER['HTTPS']) && strtolower($_SERVER['HTTPS']) !== 'off') {
        return true;
    } elseif ( isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https' ) {
        return true;
    } elseif ( !empty($_SERVER['HTTP_FRONT_END_HTTPS']) && strtolower($_SERVER['HTTP_FRONT_END_HTTPS']) !== 'off') {
        return true;
    }
    return false;
}


// json编码
function json(array $string = array(), $zh = 1) {
    @header("Content-type: application/json");
    if ($zh == 1) $json = json_encode($string, JSON_NUMERIC_CHECK | JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    else $json = json_encode($string, 320);
    return $json;
}

// 后台检测
$islogin = 0;
if (is_array(@$_SESSION["admin"])) {
	$username = @$_SESSION["admin"]["username"];
	$password = @$_SESSION["admin"]["password"];
	if (@$username == $admin_config["username"] && $password == $admin_config["password"]) {
		$islogin = 1;
	}
}

?>